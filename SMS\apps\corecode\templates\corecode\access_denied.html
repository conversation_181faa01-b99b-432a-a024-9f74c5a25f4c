{% extends 'base.html' %}
{% load static %}

{% block title %}Access Denied{% endblock %}

{% block breadcrumb-left %}
<li class="breadcrumb-item active" aria-current="page">Access Denied</li>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-exclamation-triangle{% endblock title-icon %}

{% comment %} {% block title %}Access Denied{% endblock title %} {% endcomment %}

{% block subtitle %}You don't have permission to access this page{% endblock subtitle %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Access Denied
                    </h4>
                </div>
                <div class="card-body text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-lock fa-5x text-danger mb-3"></i>
                        <h3 class="text-danger">Permission Required</h3>
                        <p class="lead text-muted">{{ error_message }}</p>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>What can you do?</strong>
                        <ul class="list-unstyled mt-2 mb-0">
                            <li>• Login with an administrator account</li>
                            <li>• Contact your system administrator for access</li>
                            <li>• Go back to your appropriate dashboard</li>
                        </ul>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-3 mb-3">
                            <a href="{{ login_url }}?next={{ requested_url|urlencode }}" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Login as Admin
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button type="button" class="btn btn-success btn-lg w-100" data-bs-toggle="modal" data-bs-target="#quickLoginModal">
                                <i class="fas fa-bolt me-2"></i>
                                Quick Login
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-lg dropdown-toggle w-100" type="button" id="dashboardDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    Go to Dashboard
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dashboardDropdown">
                                    <li><a class="dropdown-item" href="/teacher-dashboard/dashboard/">
                                        <i class="fas fa-chalkboard-teacher me-2"></i>Teacher Dashboard
                                    </a></li>
                                    <li><a class="dropdown-item" href="/student-dashboard/dashboard/">
                                        <i class="fas fa-user-graduate me-2"></i>Student Dashboard
                                    </a></li>
                                    <li><a class="dropdown-item" href="/account-dashboard/dashboard/">
                                        <i class="fas fa-user me-2"></i>Account Dashboard
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button onclick="history.back()" class="btn btn-outline-dark btn-lg w-100">
                                <i class="fas fa-arrow-left me-2"></i>
                                Go Back
                            </button>
                        </div>
                    </div>

                    <hr class="my-4">

                    <div class="text-muted">
                        <small>
                            <i class="fas fa-question-circle me-1"></i>
                            Need help? Contact your system administrator or 
                            <a href="mailto:<EMAIL>">support team</a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Login Modal -->
<div class="modal fade" id="quickLoginModal" tabindex="-1" aria-labelledby="quickLoginModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickLoginModalLabel">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    Quick Admin Login
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{{ login_url }}">
                {% csrf_token %}
                <input type="hidden" name="next" value="{{ requested_url }}">
                <input type="hidden" name="user_type" value="admin">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <small>Only administrator accounts can access this page.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Login
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Auto-focus on username field when modal opens
document.getElementById('quickLoginModal').addEventListener('shown.bs.modal', function () {
    document.getElementById('username').focus();
});
</script>
{% endblock %}
