{% extends 'TeacherDashboard/base.html' %}
{% load widget_tweaks %}
{% load static %}

{% block title %}
  {% if object %}
    Update Student - UDISE+ Format
  {% else %}
    Add New Student - UDISE+ Format
  {% endif %}
{% endblock title %}

{% block extrastyle %}
<!-- Enhanced CSS Libraries -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<style>
  /* Enhanced UDISE+ Form Styling */
  :root {
    --primary-color: #0e4686;
    --secondary-color: #667eea;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --text-muted: #6c757d;
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  }

  body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    min-height: 100vh;
  }

  .content-wrapper {
    background: transparent;
  }

  .card {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: 16px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
  }

  .card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-bottom: none;
    padding: 1.5rem 2rem;
  }

  .card-body {
    padding: 2rem;
  }

  .udise-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 2.5rem;
    border-radius: 16px;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow);
  }

  .udise-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
  }

  .udise-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .udise-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
  }

  .session-info {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.75rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  /* Enhanced Progress Bar */
  .progress {
    height: 6px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.2);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .progress-bar {
    background: linear-gradient(90deg, var(--success-color) 0%, var(--info-color) 100%);
    border-radius: 10px;
    transition: width 0.6s ease;
    position: relative;
    overflow: hidden;
  }

  .progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
      -45deg,
      rgba(255, 255, 255, .2) 25%,
      transparent 25%,
      transparent 50%,
      rgba(255, 255, 255, .2) 50%,
      rgba(255, 255, 255, .2) 75%,
      transparent 75%,
      transparent
    );
    background-size: 50px 50px;
    animation: move 2s linear infinite;
  }

  @keyframes move {
    0% {
      background-position: 0 0;
    }
    100% {
      background-position: 50px 50px;
    }
  }

  .udise-sections-container {
    background: white;
    border-radius: 16px;
    padding: 2.5rem;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
  }

  .udise-sections-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--success-color));
  }

  /* Enhanced Form Fields */
  .udise-field-group {
    position: relative;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
  }

  .udise-field-group:hover {
    transform: translateY(-2px);
  }

  .udise-field-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    display: block;
    font-size: 0.95rem;
  }

  .udise-field-number {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    text-align: center;
    line-height: 24px;
    font-size: 0.8rem;
    font-weight: 700;
    margin-right: 0.5rem;
  }

  .form-control, .form-select {
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: #fafbfc;
  }

  .form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(14, 70, 134, 0.15);
    background: white;
    transform: translateY(-1px);
  }

  .udise-section {
    display: none;
  }

  .udise-section.active {
    display: block;
    animation: fadeIn 0.3s ease-in;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  /* Enhanced Validation Styling */
  .form-control.is-invalid, .form-select.is-invalid {
    border-color: var(--danger-color);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    animation: shake 0.5s ease-in-out;
  }

  .form-control.is-valid, .form-select.is-valid {
    border-color: var(--success-color);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.38 1.38 3.72-3.72.94.94-4.66 4.66z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
  }

  .invalid-feedback {
    display: block;
    color: var(--danger-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
    font-weight: 500;
    animation: fadeIn 0.3s ease;
  }

  .valid-feedback {
    display: block;
    color: var(--success-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
    font-weight: 500;
  }

  .udise-field-group {
    margin-bottom: 1.5rem;
    position: relative;
  }

  .udise-field-label {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
  }

  .udise-field-number {
    display: inline-block;
    background: #3498db;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    text-align: center;
    line-height: 24px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-right: 0.5rem;
  }

  .udise-rule-reference {
    font-size: 0.8rem;
    color: #7f8c8d;
    font-style: italic;
  }

  .udise-field-sublabel {
    font-size: 0.8rem;
    color: #e74c3c;
    margin-bottom: 0.5rem;
  }

  .field-validation-indicator {
    position: absolute;
    top: -8px;
    right: 0;
    background: #e74c3c;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: bold;
  }

  .field-validation-indicator.field-required {
    background: #e74c3c;
  }

  .field-validation-indicator.field-optional {
    background: #95a5a6;
  }

  .form-control, .form-select {
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
  }

  .form-control:focus, .form-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    outline: none;
  }

  .form-control.is-valid, .form-select.is-valid {
    border-color: #27ae60;
  }

  .form-control.is-invalid, .form-select.is-invalid {
    border-color: #e74c3c;
  }

  /* Enhanced Section Navigation */
  .section-navigation {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem;
    border-radius: 16px;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(255, 255, 255, 0.8);
  }

  .section-nav-btn {
    background: white;
    border: 2px solid var(--border-color);
    color: var(--primary-color);
    padding: 0.75rem 1.25rem;
    border-radius: 12px;
    margin: 0.25rem;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
  }

  .section-nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
  }

  .section-nav-btn:hover::before {
    left: 100%;
  }

  .section-nav-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(14, 70, 134, 0.2);
  }

  .section-nav-btn.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-color: var(--primary-color);
    color: white;
    box-shadow: 0 6px 20px rgba(14, 70, 134, 0.3);
  }

  .section-nav-btn.completed {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
    border-color: var(--success-color);
    color: white;
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
  }

  .section-nav-btn.completed::after {
    content: '✓';
    position: absolute;
    top: -5px;
    right: -5px;
    background: white;
    color: var(--success-color);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .progress-container {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }

  .progress {
    height: 8px;
    border-radius: 4px;
    background-color: #ecf0f1;
  }

  .progress-bar {
    background: linear-gradient(90deg, #3498db, #2ecc71);
    border-radius: 4px;
    transition: width 0.3s ease;
  }

  /* Enhanced Button Styles */
  .btn {
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .btn:hover::before {
    left: 100%;
  }

  .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }

  .btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(14, 70, 134, 0.3);
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #0a3a6b 0%, #5a6fd8 100%);
    box-shadow: 0 8px 25px rgba(14, 70, 134, 0.4);
    color: white;
  }

  .btn-secondary {
    background: linear-gradient(135deg, var(--text-muted) 0%, #8e9aaf 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
  }

  .btn-secondary:hover {
    background: linear-gradient(135deg, #5a6c7d 0%, #7a8699 100%);
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
    color: white;
  }

  .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .btn:disabled:hover {
    transform: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .college-header {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    margin-bottom: 2rem;
  }

  .college-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .college-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 0;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .udise-header {
      padding: 1.5rem;
      text-align: center;
    }

    .udise-title {
      font-size: 1.5rem;
    }

    .udise-subtitle {
      font-size: 1rem;
    }

    .session-info {
      margin-top: 1rem;
      text-align: center;
    }

    .udise-sections-container {
      padding: 1rem;
    }

    .section-nav-btn {
      font-size: 0.8rem;
      padding: 0.4rem 0.8rem;
    }

    .college-header h1 {
      font-size: 2rem;
    }

    .college-header p {
      font-size: 1rem;
    }
  }

  /* Print Styles */
  @media print {
    .section-navigation,
    .progress-container,
    .btn,
    .udise-header .session-info {
      display: none !important;
    }

    .udise-section {
      display: block !important;
      page-break-inside: avoid;
    }

    .card {
      box-shadow: none;
      border: 1px solid #ddd;
    }
  }
</style>
{% endblock extrastyle %}

{% block content %}
<!-- Bootstrap check message -->
<div id="bootstrap-check-message" class="alert alert-warning alert-dismissible fade show mb-3" role="alert" style="display: none;">
  <strong>Warning!</strong> Bootstrap may not be loaded correctly. Some features might not work properly.
  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>

<!-- Edit Student Details Card -->
<div class="card mb-4 border-0 shadow-sm">
  <div class="card-header bg-white d-flex align-items-center justify-content-between">
    <h5 class="mb-0 fw-bold">
      {% if object %}
        <i class="fas fa-user-edit text-primary me-2"></i> Edit Student Details
      {% else %}
        <i class="fas fa-user-plus text-primary me-2"></i> Add New Student
      {% endif %}
    </h5>
    <div>
      <a href="{% url 'teacher_students_list' %}" class="btn btn-sm btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i> Back to List
      </a>
    </div>
  </div>
  <div class="card-body p-0">

    <!-- Form starts here -->
    <form method="POST" enctype="multipart/form-data" id="udiseStudentForm" class="needs-validation" novalidate>
      {% csrf_token %}

      <!-- Progress Indicator -->
      <div class="progress mb-4" style="height: 4px; background-color: #e9ecef; border-radius: 0; max-width: 1200px; margin-left: auto; margin-right: auto;">
        <div class="progress-bar" role="progressbar" style="width: 20%; background-color: #0e4686;" id="form-progress-bar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
      </div>

      <!-- UDISE+ Header -->
      <div class="udise-header d-flex align-items-center mb-4">
        <div class="d-flex flex-column">
          <h2 class="udise-title mb-0">{{ profile.college_name | default:"MySchool" }}</h2>
          <p class="udise-subtitle">{{ profile.college_address | default:"College Address" }}</p>
        </div>

        <div class="ms-auto session-info">
          <span id="current-time">12:00 PM</span> | Academic Year: 2023-24
        </div>
      </div>

      <!-- Section Tabs -->
      <div class="container-fluid px-4 py-3">
        <div class="row">
          <div class="col-12">
            <div class="udise-section-tabs-row d-flex flex-row justify-content-between">
              <div class="udise-section-tab active" data-section="general-profile" data-progress="20">
                <span class="tab-number">1</span> <span class="tab-text">General Profile</span>
              </div>
              <div class="udise-section-tab" data-section="enrolment-profile" data-progress="40">
                <span class="tab-number">2</span> <span class="tab-text">Enrolment Profile</span>
              </div>
              <div class="udise-section-tab" data-section="facility-profile" data-progress="60">
                <span class="tab-number">3</span> <span class="tab-text">Facility Profile</span>
              </div>
              <div class="udise-section-tab" data-section="document-upload" data-progress="80">
                <span class="tab-number">4</span> <span class="tab-text">Documents</span>
              </div>
              <div class="udise-section-tab" data-section="profile-preview" data-progress="100">
                <span class="tab-number">5</span> <span class="tab-text">Profile Preview</span>
              </div>
            </div>
            <div class="mt-3">
              <h5 class="text-primary">General Profile Information</h5>
            </div>
          </div>
        </div>
      </div>

      <!-- Sections Container -->
      <div class="udise-sections-container position-relative" style="min-height: 500px;">
        <!-- Section 1: General Profile -->
        <div id="general-profile" class="udise-section active">
          <div class="row">
          <!-- Student's Name -->
          <div class="col-md-4 mb-4">
            <div class="udise-field-group">
              <span class="field-validation-indicator field-required">Required</span>
              <label class="udise-field-label">
                <span class="udise-field-number">1</span> Student's Name
                <span class="udise-rule-reference">(as Per School record/School Admission Register)</span>
              </label>
              <div class="udise-field-sublabel">
                <span class="text-danger">*</span> <span class="udise-rule-reference"></span>
              </div>
              {{ form.fullname|add_class:"form-control"|attr:"required"|attr:"placeholder:Enter student's full name" }}
              <div class="invalid-feedback">Student name is required</div>
            </div>
          </div>

          <!-- Gender -->
          <div class="col-md-4 mb-4">
            <div class="udise-field-group">
              <span class="field-validation-indicator field-required">Required</span>
              <label class="udise-field-label">
                <span class="udise-field-number">2</span> Gender
                <span class="udise-rule-reference">(as Per School record/School Admission Register)</span>
              </label>
              <div class="udise-field-sublabel">
                <span class="text-danger">*</span> <span class="udise-rule-reference"></span>
              </div>
              {{ form.gender|add_class:"form-select"|attr:"required" }}
              <div class="invalid-feedback">Please select a gender</div>
            </div>
          </div>

          <!-- Date of Birth -->
          <div class="col-md-4 mb-4">
            <div class="udise-field-group">
              <span class="field-validation-indicator field-required">Required</span>
              <label class="udise-field-label">
                <span class="udise-field-number">3</span> Date of Birth
                <span class="udise-rule-reference">(as Per School record/School Admission Register)</span>
              </label>
              <div class="udise-field-sublabel">
                <span class="text-danger">*</span> <span class="udise-rule-reference"></span>
                <a href="#" class="text-primary" data-bs-toggle="tooltip" title="Click to view age matrix for different classes">Age Matrix</a>
              </div>
              <div class="date-input-group">
                {{ form.date_of_birth|add_class:"form-control"|attr:"required" }}
                <i class="fas fa-calendar"></i>
              </div>
              <div class="invalid-feedback">Date of birth is required</div>
            </div>
          </div>

          <!-- Mother's Name -->
          <div class="col-md-4 mb-4">
            <div class="udise-field-group">
              <span class="field-validation-indicator field-optional">Optional</span>
              <label class="udise-field-label">
                <span class="udise-field-number">4</span> Mother's Name
              </label>
              <div class="udise-field-sublabel">
                <span class="udise-rule-reference"></span>
              </div>
              {{ form.Mother_name|add_class:"form-control"|attr:"placeholder:Enter mother's name" }}
              <div class="invalid-feedback"></div>
            </div>
          </div>

          <!-- Father's Name -->
          <div class="col-md-4 mb-4">
            <div class="udise-field-group">
              <label class="udise-field-label">
                <span class="udise-field-number">5</span> Father's Name
              </label>
              <div class="udise-field-sublabel">
                <span class="udise-rule-reference"></span>
              </div>
              {{ form.Father_name|add_class:"form-control"|attr:"placeholder:Enter father's name" }}
              <div class="invalid-feedback"></div>
            </div>
          </div>

          <!-- Current Class -->
          <div class="col-md-4 mb-4">
            <div class="udise-field-group">
              <span class="field-validation-indicator field-required">Required</span>
              <label class="udise-field-label">
                <span class="udise-field-number">6</span> Current Class
              </label>
              <div class="udise-field-sublabel">
                <span class="text-danger">*</span> <span class="udise-rule-reference"></span>
              </div>
              {{ form.current_class|add_class:"form-select"|attr:"required" }}
              <div class="invalid-feedback">Please select a class</div>
            </div>
          </div>

          <!-- Section -->
          <div class="col-md-4 mb-4">
            <div class="udise-field-group">
              <label class="udise-field-label">
                <span class="udise-field-number">7</span> Section
              </label>
              <div class="udise-field-sublabel">
                <span class="udise-rule-reference"></span>
              </div>
              {{ form.section|add_class:"form-select" }}
              <div class="invalid-feedback"></div>
            </div>
          </div>

          <!-- Mobile Number -->
          <div class="col-md-4 mb-4">
            <div class="udise-field-group">
              <label class="udise-field-label">
                <span class="udise-field-number">8</span> Mobile Number
              </label>
              <div class="udise-field-sublabel">
                <span class="udise-rule-reference"></span>
              </div>
              {{ form.mobile_number|add_class:"form-control"|attr:"placeholder:Enter mobile number" }}
              <div class="invalid-feedback"></div>
            </div>
          </div>

          <!-- Address -->
          <div class="col-md-8 mb-4">
            <div class="udise-field-group">
              <label class="udise-field-label">
                <span class="udise-field-number">9</span> Address
              </label>
              <div class="udise-field-sublabel">
                <span class="udise-rule-reference"></span>
              </div>
              {{ form.address|add_class:"form-control"|attr:"placeholder:Enter complete address" }}
              <div class="invalid-feedback"></div>
            </div>
          </div>

          <!-- Student Photo -->
          <div class="col-md-4 mb-4">
            <div class="udise-field-group">
              <label class="udise-field-label">
                <span class="udise-field-number">10</span> Student Photo
              </label>
              <div class="udise-field-sublabel">
                <span class="udise-rule-reference">Upload passport size photo</span>
              </div>
              <div class="text-center mb-3">
                <div class="photo-preview mb-2">
                  {% if object and object.passport %}
                    <img src="{{ object.passport.url }}" alt="Student Photo" class="img-thumbnail" style="height: 150px; width: 150px; object-fit: cover;">
                  {% else %}
                    <div class="placeholder-image d-flex align-items-center justify-content-center bg-light" style="height: 150px; width: 150px; border-radius: 5px;">
                      <i class="fas fa-user fa-4x text-secondary"></i>
                    </div>
                  {% endif %}
                </div>
                {{ form.passport|add_class:"form-control" }}
              </div>
            </div>
          </div>
        </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-between mt-4 px-4 pb-4">
          <a href="{% url 'teacher_students_list' %}" class="btn btn-secondary">
            <i class="fas fa-times me-1"></i> Cancel
          </a>
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-1"></i>
            {% if object %}Update Student{% else %}Save Student{% endif %}
          </button>
        </div>
      </div>
    </form>
  </div>
</div>

<!-- Enhanced Floating Elements -->
<!-- Floating Progress Indicator -->
<div class="floating-progress" id="floating-progress">
  <div class="progress-circle">
    <svg class="progress-ring" width="60" height="60">
      <circle class="progress-ring-circle" stroke="#dee2e6" stroke-width="4" fill="transparent" r="26" cx="30" cy="30"/>
      <circle class="progress-ring-progress" stroke="url(#progressGradient)" stroke-width="4" fill="transparent" r="26" cx="30" cy="30"/>
      <defs>
        <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:var(--primary-color);stop-opacity:1" />
          <stop offset="100%" style="stop-color:var(--secondary-color);stop-opacity:1" />
        </linearGradient>
      </defs>
    </svg>
    <div class="progress-text">0%</div>
  </div>
  <div class="progress-tooltip">Form Completion</div>
</div>

<!-- Enhanced Floating Help Button -->
<div class="floating-help" id="floating-help">
  <i class="fas fa-question"></i>
  <div class="help-tooltip" id="help-tooltip">
    <h5><i class="fas fa-graduation-cap me-2 text-primary"></i>UDISE+ Student Form Help</h5>
    <div class="help-content">
      <div class="help-item">
        <i class="fas fa-user-circle text-info me-2"></i>
        <div>
          <strong>Personal Information:</strong> Enter basic student details as per official records
        </div>
      </div>
      <div class="help-item">
        <i class="fas fa-asterisk text-danger me-2"></i>
        <div>
          <strong>Required Fields:</strong> Fields marked with * are mandatory for submission
        </div>
      </div>
      <div class="help-item">
        <i class="fas fa-save text-success me-2"></i>
        <div>
          <strong>Auto-Save:</strong> Your progress is automatically saved every 10 seconds
        </div>
      </div>
      <div class="help-item">
        <i class="fas fa-keyboard text-warning me-2"></i>
        <div>
          <strong>Navigation:</strong> Use Tab key to move between fields quickly
        </div>
      </div>
    </div>
    <div class="help-footer">
      <small class="text-muted">
        <i class="fas fa-phone me-1"></i>Need help? Contact IT Support
      </small>
    </div>
  </div>
</div>

<!-- Floating Auto-Save Indicator -->
<div class="floating-autosave" id="floating-autosave" style="display: none;">
  <i class="fas fa-cloud-upload-alt"></i>
  <span>Saving...</span>
</div>
{% endblock content %}

{% block extrajs %}
<script>
// Update current time
function updateTime() {
  const now = new Date();
  const timeString = now.toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit',
    hour12: true 
  });
  document.getElementById('current-time').textContent = timeString;
}

// Update time every minute
setInterval(updateTime, 60000);
updateTime(); // Initial call

// Section navigation
document.querySelectorAll('.section-nav-btn').forEach(btn => {
  btn.addEventListener('click', function() {
    const targetSection = this.dataset.section;
    
    // Update navigation buttons
    document.querySelectorAll('.section-nav-btn').forEach(b => b.classList.remove('active'));
    this.classList.add('active');
    
    // Update sections
    document.querySelectorAll('.udise-section').forEach(section => {
      section.classList.remove('active');
    });
    document.getElementById(targetSection).classList.add('active');
  });
});

// Form validation and progress
function updateFormProgress() {
  const form = document.getElementById('udiseForm');
  const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
  let filledInputs = 0;
  
  inputs.forEach(input => {
    if (input.value.trim() !== '') {
      filledInputs++;
    }
  });
  
  const progress = Math.round((filledInputs / inputs.length) * 100);
  document.getElementById('formProgress').style.width = progress + '%';
  document.getElementById('progressPercentage').textContent = progress + '%';
}

// Update progress on input change
document.addEventListener('input', updateFormProgress);
document.addEventListener('change', updateFormProgress);

// Initial progress update
updateFormProgress();

// Enhanced Form Validation with Real-time Feedback
function setupRealTimeValidation() {
  const form = document.getElementById('udise-form');
  if (!form) return;

  const inputs = form.querySelectorAll('input, select, textarea');

  inputs.forEach(input => {
    input.addEventListener('blur', function() {
      validateField(this);
    });

    input.addEventListener('input', function() {
      if (this.classList.contains('is-invalid')) {
        validateField(this);
      }
    });
  });
}

function validateField(field) {
  const value = field.value.trim();
  const isRequired = field.hasAttribute('required');
  let isValid = true;
  let errorMessage = '';

  // Remove existing validation classes
  field.classList.remove('is-valid', 'is-invalid');

  // Required field validation
  if (isRequired && !value) {
    isValid = false;
    errorMessage = 'This field is required';
  }

  // Email validation
  if (field.type === 'email' && value) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      isValid = false;
      errorMessage = 'Please enter a valid email address';
    }
  }

  // Phone number validation
  if (field.name && field.name.includes('mobile') && value) {
    const phoneRegex = /^[6-9]\d{9}$/;
    if (!phoneRegex.test(value)) {
      isValid = false;
      errorMessage = 'Please enter a valid 10-digit mobile number';
    }
  }

  // Aadhaar validation
  if (field.name && field.name.includes('aadhar') && value) {
    const aadhaarRegex = /^\d{12}$/;
    if (!aadhaarRegex.test(value)) {
      isValid = false;
      errorMessage = 'Please enter a valid 12-digit Aadhaar number';
    }
  }

  // Apply validation styling
  if (isValid && value) {
    field.classList.add('is-valid');
    removeErrorMessage(field);
  } else if (!isValid) {
    field.classList.add('is-invalid');
    showErrorMessage(field, errorMessage);
  }

  return isValid;
}

function showErrorMessage(field, message) {
  removeErrorMessage(field);

  const errorDiv = document.createElement('div');
  errorDiv.className = 'invalid-feedback';
  errorDiv.textContent = message;
  errorDiv.setAttribute('data-field-error', field.name || field.id);

  field.parentNode.appendChild(errorDiv);
}

function removeErrorMessage(field) {
  const existingError = field.parentNode.querySelector(`[data-field-error="${field.name || field.id}"]`);
  if (existingError) {
    existingError.remove();
  }
}

// Auto-save functionality
let autoSaveTimer;
let hasUnsavedChanges = false;

function setupAutoSave() {
  const form = document.getElementById('udise-form');
  if (!form) return;

  form.addEventListener('input', function() {
    hasUnsavedChanges = true;
    clearTimeout(autoSaveTimer);
    autoSaveTimer = setTimeout(autoSave, 10000); // Auto-save after 10 seconds
  });

  // Warn before leaving with unsaved changes
  window.addEventListener('beforeunload', function(e) {
    if (hasUnsavedChanges) {
      e.preventDefault();
      e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
      return e.returnValue;
    }
  });
}

function autoSave() {
  if (!hasUnsavedChanges) return;

  showNotification('Auto-saving draft...', 'info', 2000);

  // Here you can implement actual auto-save logic
  // For now, we'll just simulate it
  setTimeout(() => {
    hasUnsavedChanges = false;
    showNotification('Draft saved successfully', 'success', 2000);
  }, 1500);
}

// Enhanced notification system
function showNotification(message, type = 'info', duration = 3000) {
  // Remove existing notifications
  document.querySelectorAll('.notification-toast').forEach(n => n.remove());

  const notification = document.createElement('div');
  notification.className = `alert alert-${type} notification-toast animate__animated animate__fadeInRight`;
  notification.innerHTML = `
    <i class="fas fa-${getIconForType(type)} me-2"></i>
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;

  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    max-width: 400px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    border: none;
    backdrop-filter: blur(10px);
  `;

  document.body.appendChild(notification);

  if (duration > 0) {
    setTimeout(() => {
      if (notification.parentElement) {
        notification.classList.remove('animate__fadeInRight');
        notification.classList.add('animate__fadeOutRight');
        setTimeout(() => notification.remove(), 500);
      }
    }, duration);
  }
}

function getIconForType(type) {
  switch(type) {
    case 'success': return 'check-circle';
    case 'error': case 'danger': return 'exclamation-circle';
    case 'warning': return 'exclamation-triangle';
    default: return 'info-circle';
  }
}

// Initialize enhanced features
document.addEventListener('DOMContentLoaded', function() {
  setupRealTimeValidation();
  setupAutoSave();

  // Show welcome message
  setTimeout(() => {
    showNotification('UDISE+ Form loaded successfully. Auto-save is enabled.', 'success', 4000);
  }, 1000);
});

</script>
{% endblock extrajs %}
